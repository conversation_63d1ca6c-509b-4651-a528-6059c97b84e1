@page "/users"
@using CoreHub.Shared.Services
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Components
@inject IUserManagementService UserManagementService
@inject IJobTypeService JobTypeService
@inject IDepartmentService DepartmentService
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>用户管理</PageTitle>

<PermissionView RequiredPermission="UserManagement.View">
    <ChildContent>
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge">
            <MudPaper Elevation="2" Class="pa-4">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <MudText Typo="Typo.h4" Color="Color.Primary">用户管理</MudText>
                    <MudSpacer />
                    <PermissionView RequiredPermission="UserManagement.Create">
                        <MudButton Variant="Variant.Filled" 
                                   Color="Color.Primary" 
                                   StartIcon="@Icons.Material.Filled.PersonAdd"
                                   OnClick="ShowCreateDialog">
                            新增用户
                        </MudButton>
                    </PermissionView>
                </div>

                <!-- 搜索栏 -->
                <div style="display: flex; gap: 12px; margin-bottom: 20px; align-items: center;">
                    <MudTextField @bind-Value="searchKeyword"
                                  @onkeypress="OnSearchKeyPress"
                                  Label="搜索用户名"
                                  Variant="Variant.Outlined"
                                  Style="width: 300px;"
                                  Adornment="Adornment.End"
                                  AdornmentIcon="@Icons.Material.Filled.Search" />
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Primary" 
                               StartIcon="@Icons.Material.Filled.Search"
                               OnClick="SearchUsers">
                        搜索
                    </MudButton>
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Default" 
                               StartIcon="@Icons.Material.Filled.Refresh"
                               OnClick="LoadUsers">
                        刷新
                    </MudButton>
                </div>

                @if (isLoading)
                {
                    <div style="display: flex; justify-content: center; padding: 40px;">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </div>
                }
                else if (users?.Any() == true)
                {
                    <MudDataGrid Items="@users" 
                                 Hover="true" 
                                 Striped="true"
                                 Dense="true"
                                 Elevation="0">
                        <Columns>
                            <PropertyColumn Property="x => x.Username" Title="用户名">
                                <CellTemplate>
                                    <MudText Typo="Typo.body1"><strong>@context.Item.Username</strong></MudText>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.DisplayName" Title="显示名称" />
                            
                            <PropertyColumn Property="x => x.Email" Title="邮箱" />
                            
                            <PropertyColumn Property="x => x.Phone" Title="手机号" />

                            <TemplateColumn Title="所属部门" Sortable="false">
                                <CellTemplate>
                                    @if (context.Item.DepartmentId.HasValue)
                                    {
                                        var department = departments.FirstOrDefault(d => d.Id == context.Item.DepartmentId.Value);
                                        if (department != null)
                                        {
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Small" Class="mr-1" />
                                                <MudText Typo="Typo.body2">@department.Name</MudText>
                                            </div>
                                        }
                                        else
                                        {
                                            <MudText Typo="Typo.body2" Color="Color.Warning">部门不存在</MudText>
                                        }
                                    }
                                    else
                                    {
                                        <MudText Typo="Typo.body2" Color="Color.Default">未分配</MudText>
                                    }
                                </CellTemplate>
                            </TemplateColumn>

                            <PropertyColumn Property="x => x.IsEnabled" Title="状态">
                                <CellTemplate>
                                    <div style="display: flex; gap: 4px;">
                                        @if (context.Item.IsEnabled)
                                        {
                                            <MudChip Color="Color.Success" Size="Size.Small">已启用</MudChip>
                                        }
                                        else
                                        {
                                            <MudChip Color="Color.Error" Size="Size.Small">已禁用</MudChip>
                                        }
                                        @if (context.Item.IsLocked)
                                        {
                                            <MudChip Color="Color.Warning" Size="Size.Small">已锁定</MudChip>
                                        }
                                    </div>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.LoginFailureCount" Title="登录失败次数">
                                <CellTemplate>
                                    @if (context.Item.LoginFailureCount > 0)
                                    {
                                        <MudChip Color="Color.Error" Size="Size.Small">@context.Item.LoginFailureCount</MudChip>
                                    }
                                    else
                                    {
                                        <MudText Color="Color.Default">0</MudText>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.LastLoginTime" Title="最后登录时间">
                                <CellTemplate>
                                    @if (context.Item.LastLoginTime != null)
                                    {
                                        <MudText>@context.Item.LastLoginTime.Value.ToString("yyyy-MM-dd HH:mm")</MudText>
                                    }
                                    else
                                    {
                                        <MudText Color="Color.Default">从未登录</MudText>
                                    }
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <PropertyColumn Property="x => x.CreatedAt" Title="创建时间">
                                <CellTemplate>
                                    <MudText>@context.Item.CreatedAt.ToString("yyyy-MM-dd HH:mm")</MudText>
                                </CellTemplate>
                            </PropertyColumn>
                            
                            <TemplateColumn Title="操作" Sortable="false">
                                <CellTemplate>
                                    <div style="display: flex; gap: 4px;">
                                        <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                                      Size="Size.Small"
                                                      Color="Color.Info"
                                                      title="查看详情"
                                                      OnClick="() => ViewUserDetail(context.Item)" />
                                        
                                        <PermissionView RequiredPermission="UserManagement.Edit">
                                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                                          Size="Size.Small"
                                                          Color="Color.Warning"
                                                          title="编辑"
                                                          OnClick="() => ShowEditDialog(context.Item)" />
                                        </PermissionView>
                                        
                                        <PermissionView RequiredPermission="UserManagement.AssignRoles">
                                            <MudIconButton Icon="@Icons.Material.Filled.Group"
                                                          Size="Size.Small"
                                                          Color="Color.Success"
                                                          title="角色分配"
                                                          OnClick="() => ShowRoleDialog(context.Item)" />
                                        </PermissionView>

                                        <PermissionView RequiredPermission="UserManagement.Edit">
                                            <MudIconButton Icon="@Icons.Material.Filled.Work"
                                                          Size="Size.Small"
                                                          Color="Color.Primary"
                                                          title="工种分配"
                                                          OnClick="() => ShowJobTypeDialog(context.Item)" />
                                        </PermissionView>
                                        
                                        @if (context.Item.IsLocked)
                                        {
                                            <PermissionView RequiredPermission="UserManagement.Edit">
                                                <MudIconButton Icon="@Icons.Material.Filled.LockOpen"
                                                              Size="Size.Small"
                                                              Color="Color.Warning"
                                                              title="解锁"
                                                              OnClick="() => UnlockUser(context.Item)" />
                                            </PermissionView>
                                        }
                                        else
                                        {
                                            <PermissionView RequiredPermission="UserManagement.Edit">
                                                <MudIconButton Icon="@Icons.Material.Filled.Lock"
                                                              Size="Size.Small"
                                                              Color="Color.Error"
                                                              title="锁定"
                                                              OnClick="() => LockUser(context.Item)" />
                                            </PermissionView>
                                        }
                                        
                                        <PermissionView RequiredPermission="UserManagement.Delete">
                                            <MudIconButton Icon="@Icons.Material.Filled.Delete"
                                                          Size="Size.Small"
                                                          Color="Color.Error"
                                                          title="删除"
                                                          OnClick="() => DeleteUser(context.Item)" />
                                        </PermissionView>
                                    </div>
                                </CellTemplate>
                            </TemplateColumn>
                        </Columns>
                    </MudDataGrid>
                }
                else
                {
                    <div style="display: flex; flex-direction: column; align-items: center; padding: 60px 20px;">
                        <MudIcon Icon="@Icons.Material.Filled.PeopleAlt" 
                                 Size="Size.Large" 
                                 Color="Color.Default" 
                                 Style="font-size: 4rem; margin-bottom: 16px;" />
                        <MudText Typo="Typo.h6" Color="Color.Default" Class="mb-4">暂无用户数据</MudText>
                    </div>
                }
            </MudPaper>
        </MudContainer>

        <!-- 测试账号信息 -->
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
            <MudPaper Elevation="2" Class="pa-4">
                <MudText Typo="Typo.h5" Class="mb-4" Color="Color.Primary">测试账号信息</MudText>
                <MudGrid>
                    <MudItem xs="12" md="4">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">管理员账号</MudText>
                                <MudText><strong>用户名:</strong> admin</MudText>
                                <MudText><strong>密码:</strong> admin123</MudText>
                                <MudText><strong>权限:</strong> 全部功能</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">操作员账号</MudText>
                                <MudText><strong>用户名:</strong> operator</MudText>
                                <MudText><strong>密码:</strong> op123</MudText>
                                <MudText><strong>权限:</strong> 设备相关功能</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                    
                    <MudItem xs="12" md="4">
                        <MudCard>
                            <MudCardContent>
                                <MudText Typo="Typo.h6" Class="mb-2">访客账号</MudText>
                                <MudText><strong>用户名:</strong> viewer</MudText>
                                <MudText><strong>密码:</strong> view123</MudText>
                                <MudText><strong>权限:</strong> 基础查看功能</MudText>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudContainer>

        <!-- 系统特性说明 -->
        <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="mt-4">
            <MudPaper Elevation="2" Class="pa-4">
                <MudText Typo="Typo.h5" Class="mb-4" Color="Color.Primary">系统特性</MudText>
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudText><strong>数据库:</strong> SQL Server + SqlSugar ORM</MudText>
                        <MudText><strong>密码存储:</strong> 明文密码（开发环境）</MudText>
                        <MudText><strong>权限模型:</strong> RBAC（基于角色的访问控制）</MudText>
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudText><strong>存储过程:</strong> 支持存储过程验证</MudText>
                        <MudText><strong>账户安全:</strong> 自动锁定、登录日志</MudText>
                        <MudText><strong>权限控制:</strong> 页面级、功能级权限控制</MudText>
                    </MudItem>
                </MudGrid>
                <MudAlert Severity="Severity.Warning" Class="mt-3">
                    <strong>注意:</strong> 当前系统使用明文密码存储，仅适用于开发和测试环境！
                </MudAlert>
            </MudPaper>
        </MudContainer>
    </ChildContent>
    
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Medium">
            <MudPaper Elevation="2" Class="pa-8" Style="text-align: center;">
                <MudIcon Icon="@Icons.Material.Filled.Lock" 
                         Size="Size.Large" 
                         Color="Color.Error" 
                         Style="font-size: 4rem; margin-bottom: 16px;" />
                <MudText Typo="Typo.h4" Color="Color.Error" Class="mb-4">权限不足</MudText>
                <MudText Typo="Typo.body1" Color="Color.Default">您没有权限访问用户管理页面</MudText>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</PermissionView>

<!-- 创建/编辑用户对话框 -->
@if (showUserDialog)
{
    <MudOverlay @bind-Visible="showUserDialog" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6" Style="width: 800px; max-width: 90vw; max-height: 90vh; overflow-y: auto; margin: 20px auto; position: relative;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <MudIcon Icon="@(isEditMode ? Icons.Material.Filled.Edit : Icons.Material.Filled.PersonAdd)" Class="mr-3" />
                <MudText Typo="Typo.h5">@(isEditMode ? "编辑用户" : "新增用户")</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="CloseUserDialog" />
            </div>

            <EditForm Model="currentUser" OnValidSubmit="HandleUserSubmit">
                <DataAnnotationsValidator />
                
                <MudGrid>
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentUser.Username"
                                      Label="用户名"
                                      Required="true"
                                      RequiredError="请输入用户名"
                                      Disabled="@isEditMode"
                                      Variant="Variant.Outlined" />
                        @if (isEditMode)
                        {
                            <MudText Typo="Typo.caption" Color="Color.Default">用户名创建后不可修改</MudText>
                        }
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentUser.DisplayName"
                                      Label="显示名称"
                                      Required="true"
                                      RequiredError="请输入显示名称"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    @if (!isEditMode)
                    {
                        <MudItem xs="12">
                            <MudTextField @bind-Value="currentUser.PasswordHash"
                                          Label="密码"
                                          InputType="InputType.Password"
                                          Required="true"
                                          RequiredError="请输入密码"
                                          Variant="Variant.Outlined" />
                            <MudText Typo="Typo.caption" Color="Color.Default">密码长度至少6位</MudText>
                        </MudItem>
                    }
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentUser.Email"
                                      Label="邮箱"
                                      InputType="InputType.Email"
                                      Variant="Variant.Outlined" />
                    </MudItem>
                    
                    <MudItem xs="12" sm="6">
                        <MudTextField @bind-Value="currentUser.Phone"
                                      Label="手机号"
                                      Variant="Variant.Outlined" />
                    </MudItem>

                    <MudItem xs="12" sm="6">
                        <MudSelect T="int?" @bind-Value="currentUser.DepartmentId"
                                   Label="所属部门"
                                   Clearable="true"
                                   Variant="Variant.Outlined">
                            @foreach (var dept in departments)
                            {
                                <MudSelectItem T="int?" Value="@dept.Id">
                                    <div class="d-flex align-center">
                                        <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Small" Class="mr-2" />
                                        <span>@dept.Name</span>
                                        @if (!string.IsNullOrEmpty(dept.Description))
                                        {
                                            <MudText Typo="Typo.caption" Class="ml-2 text-muted">(@dept.Description)</MudText>
                                        }
                                    </div>
                                </MudSelectItem>
                            }
                        </MudSelect>
                    </MudItem>

                    <MudItem xs="12">
                        <MudTextField @bind-Value="currentUser.Remark"
                                      Label="备注"
                                      Lines="3"
                                      Variant="Variant.Outlined" />
                    </MudItem>

                    <MudItem xs="12">
                        <MudCheckBox @bind-Value="currentUser.IsEnabled"
                                     Label="启用用户"
                                     Color="Color.Primary" />
                    </MudItem>
                </MudGrid>
                
                <ValidationSummary />
                
                <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                    <MudButton OnClick="CloseUserDialog" Color="Color.Default">取消</MudButton>
                    <MudButton ButtonType="ButtonType.Submit" 
                               Color="Color.Primary" 
                               Variant="Variant.Filled"
                               Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                            <MudText Class="ms-2">保存中...</MudText>
                        }
                        else
                        {
                            <MudText>@(isEditMode ? "更新" : "创建")</MudText>
                        }
                    </MudButton>
                </div>
            </EditForm>
        </MudPaper>
    </MudOverlay>
}

<!-- 角色分配对话框 -->
@if (showRoleDialog)
{
    <MudOverlay @bind-Visible="showRoleDialog" DarkBackground="true" Absolute="false">
        <MudPaper Class="pa-6" Style="width: 800px; max-width: 90vw; max-height: 90vh; overflow-y: auto; margin: 20px auto; position: relative;">
            <div style="display: flex; align-items: center; margin-bottom: 20px;">
                <MudIcon Icon="@Icons.Material.Filled.Group" Class="mr-3" />
                <MudText Typo="Typo.h5">角色分配 - @selectedUser?.DisplayName</MudText>
                <MudSpacer />
                <MudIconButton Icon="@Icons.Material.Filled.Close" OnClick="CloseRoleDialog" />
            </div>

            @if (isLoadingRoles)
            {
                <div style="display: flex; justify-content: center; padding: 40px;">
                    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                </div>
            }
            else
            {
                <MudGrid>
                    @foreach (var role in allRoles)
                    {
                        <MudItem xs="12" md="6">
                            <MudCard Class="@(userRoles.Contains(role.Id) ? "mud-border-success" : "")">
                                <MudCardContent>
                                    <div style="display: flex; align-items: flex-start; gap: 12px;">
                                        <MudCheckBox Value="@GetRoleChecked(role.Id)"
                                                     ValueChanged="@((bool value) => ToggleRole(role.Id, value))"
                                                     Color="Color.Primary" />
                                        <div style="flex: 1;">
                                            <MudText Typo="Typo.h6">@role.Name</MudText>
                                            <MudText Typo="Typo.body2" Color="Color.Default">@role.Description</MudText>
                                            <div style="display: flex; gap: 4px; margin-top: 8px;">
                                                @if (role.IsEnabled)
                                                {
                                                    <MudChip T="string" Color="Color.Success" Size="Size.Small">启用</MudChip>
                                                }
                                                else
                                                {
                                                    <MudChip T="string" Color="Color.Error" Size="Size.Small">禁用</MudChip>
                                                }
                                                @if (role.IsSystem)
                                                {
                                                    <MudChip T="string" Color="Color.Info" Size="Size.Small">系统角色</MudChip>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </MudCardContent>
                            </MudCard>
                        </MudItem>
                    }
                </MudGrid>
            }
            
            <div style="display: flex; justify-content: flex-end; gap: 12px; margin-top: 24px;">
                <MudButton OnClick="CloseRoleDialog" Color="Color.Default">取消</MudButton>
                <MudButton Color="Color.Primary" 
                           Variant="Variant.Filled"
                           Disabled="@isSavingRoles"
                           OnClick="SaveUserRoles">
                    @if (isSavingRoles)
                    {
                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true" />
                        <MudText Class="ms-2">保存中...</MudText>
                    }
                    else
                    {
                        <MudText>保存角色</MudText>
                    }
                </MudButton>
            </div>
        </MudPaper>
    </MudOverlay>
}

@code {
    private List<User> users = new();
    private List<Role> allRoles = new();
    private List<Department> departments = new();
    private HashSet<int> userRoles = new();
    private bool isLoading = false;
    private bool isLoadingRoles = false;
    private bool isSaving = false;
    private bool isSavingRoles = false;
    private string searchKeyword = "";
    private bool showUserDialog = false;
    private bool showRoleDialog = false;
    private bool isEditMode = false;
    private User currentUser = new User();
    private User? selectedUser;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
        await LoadRoles();
        await LoadDepartments();
    }

    private async Task LoadUsers()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            var result = await UserManagementService.GetUsersAsync(1, 100);
            users = result.Users;
            @* Snackbar.Add($"加载了 {users.Count} 个用户", Severity.Success); *@
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadRoles()
    {
        try
        {
            allRoles = await UserManagementService.GetAllRolesAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载角色失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task LoadDepartments()
    {
        try
        {
            departments = await DepartmentService.GetEnabledDepartmentsAsync();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载部门失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task SearchUsers()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (string.IsNullOrWhiteSpace(searchKeyword))
            {
                await LoadUsers();
                return;
            }

            var result = await UserManagementService.GetUsersAsync(1, 100, searchKeyword);
            users = result.Users;
            Snackbar.Add($"搜索到 {users.Count} 个用户", Severity.Info);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"搜索失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchUsers();
        }
    }

    private void ShowCreateDialog()
    {
        currentUser = new User { IsEnabled = true };
        isEditMode = false;
        showUserDialog = true;
        StateHasChanged();
    }

    private void ShowEditDialog(User user)
    {
        currentUser = new User
        {
            Id = user.Id,
            Username = user.Username,
            DisplayName = user.DisplayName,
            Email = user.Email,
            Phone = user.Phone,
            DepartmentId = user.DepartmentId,
            IsEnabled = user.IsEnabled,
            Remark = user.Remark
        };
        isEditMode = true;
        showUserDialog = true;
        StateHasChanged();
    }

    private void CloseUserDialog()
    {
        showUserDialog = false;
        currentUser = new User();
        isEditMode = false;
        isSaving = false;
        StateHasChanged();
    }

    private async Task HandleUserSubmit()
    {
        await SaveUser();
    }

    private async Task SaveUser()
    {
        try
        {
            isSaving = true;
            StateHasChanged();

            if (isEditMode)
            {
                var result = await UserManagementService.UpdateUserAsync(currentUser);
                if (result.IsSuccess)
                {
                    Snackbar.Add("用户更新成功", Severity.Success);
                    CloseUserDialog();
                    await LoadUsers();
                }
                else
                {
                    Snackbar.Add($"用户更新失败: {result.ErrorMessage}", Severity.Error);
                }
            }
            else
            {
                var result = await UserManagementService.CreateUserAsync(currentUser);
                if (result.IsSuccess)
                {
                    Snackbar.Add("用户创建成功", Severity.Success);
                    CloseUserDialog();
                    await LoadUsers();
                }
                else
                {
                    Snackbar.Add($"用户创建失败: {result.ErrorMessage}", Severity.Error);
                }
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存用户失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private async Task DeleteUser(User user)
    {
        var result = await DialogService.ShowMessageBox(
            "确认删除",
            $"确定要删除用户 '{user.Username}' 吗？此操作不可恢复。",
            yesText: "删除", cancelText: "取消");

        if (result == true)
        {
            try
            {
                var deleteResult = await UserManagementService.DeleteUserAsync(user.Id);
                if (deleteResult.IsSuccess)
                {
                    Snackbar.Add("用户删除成功", Severity.Success);
                    await LoadUsers();
                }
                else
                {
                    Snackbar.Add($"用户删除失败: {deleteResult.ErrorMessage}", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除用户失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private async Task ShowRoleDialog(User user)
    {
        selectedUser = user;
        isLoadingRoles = true;
        showRoleDialog = true;
        StateHasChanged();

        try
        {
            var roles = await UserManagementService.GetUserRolesAsync(user.Id);
            userRoles = roles.Select(r => r.Id).ToHashSet();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载用户角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isLoadingRoles = false;
            StateHasChanged();
        }
    }

    private void CloseRoleDialog()
    {
        showRoleDialog = false;
        selectedUser = null;
        userRoles.Clear();
        isSavingRoles = false;
        StateHasChanged();
    }

    private bool GetRoleChecked(int roleId)
    {
        return userRoles.Contains(roleId);
    }

    private void ToggleRole(int roleId, bool isChecked)
    {
        if (isChecked)
        {
            userRoles.Add(roleId);
        }
        else
        {
            userRoles.Remove(roleId);
        }
    }

    private async Task SaveUserRoles()
    {
        if (selectedUser == null) return;

        try
        {
            isSavingRoles = true;
            StateHasChanged();

            var result = await UserManagementService.UpdateUserRolesAsync(selectedUser.Id, userRoles.ToList());
            if (result.IsSuccess)
            {
                Snackbar.Add("用户角色保存成功", Severity.Success);
                CloseRoleDialog();
            }
            else
            {
                Snackbar.Add($"用户角色保存失败: {result.ErrorMessage}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存用户角色失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            isSavingRoles = false;
            StateHasChanged();
        }
    }

    private void ViewUserDetail(User user)
    {
        Snackbar.Add($"用户详情 - {user.Username}: 显示名称={user.DisplayName}, 邮箱={user.Email}, 手机={user.Phone}", Severity.Info);
    }

    private async Task LockUser(User user)
    {
        try
        {
            await UserManagementService.LockUserAsync(user.Id, "手动锁定");
            Snackbar.Add($"用户 {user.Username} 已被锁定", Severity.Warning);
            await LoadUsers();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"锁定用户失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task UnlockUser(User user)
    {
        try
        {
            await UserManagementService.UnlockUserAsync(user.Id);
            Snackbar.Add($"用户 {user.Username} 已被解锁", Severity.Success);
            await LoadUsers();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"解锁用户失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task ShowJobTypeDialog(User user)
    {
        var parameters = new DialogParameters<UserJobTypeAssignmentDialog>
        {
            { x => x.User, user }
        };

        var options = new DialogOptions()
        {
            MaxWidth = MaxWidth.ExtraLarge,
            FullWidth = true
        };

        await DialogService.ShowAsync<UserJobTypeAssignmentDialog>($"工种分配 - {user.DisplayName}", parameters, options);
    }
}